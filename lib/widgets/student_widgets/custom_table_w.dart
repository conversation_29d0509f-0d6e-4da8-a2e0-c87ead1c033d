import 'package:bus/bloc/delete_student_cubit/delete_student_cubit.dart';
import 'package:bus/bloc/student_cubit/student_cubit.dart';
import 'package:bus/config/config_base.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/data/models/student_models/student_model.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/helpers.dart';
import 'package:bus/views/screens/add_student_screen/add_student_screen.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:share_plus/share_plus.dart';

import '../../constant/path_route_name.dart';
import '../../data/repo/buses_repo.dart';
import '../../views/custom_widgets/build_table_row_widget.dart';
import '../../views/custom_widgets/custom_text.dart';
import '../../views/screens/parent_screen/parent_screen.dart';
import '../../views/screens/student_address_screen/student_address_screen.dart';
import 'custom_container_dialog_w.dart';

class CustomTableW extends StatefulWidget {
  final List<StudentModel?>? studentInfoModels;
  final bool isFromBus;
  final int? busId;
  final String? busName;

  const CustomTableW(
      {Key? key,
      this.studentInfoModels,
      this.isFromBus = false,
      this.busId,
      this.busName})
      : super(key: key);

  @override
  State<CustomTableW> createState() => _CustomTableWState();
}

class _CustomTableWState extends State<CustomTableW> {
  String _translateGrade(String? grade) {
    if (context.locale.toString() == "ar") {
      switch (grade) {
        case 'KG':
          return 'الروضة';
        case 'secondary':
          return 'الثانوية';
        case 'Middle':
          return 'الإعدادية';
        case 'Primary':
          return 'الإبتدائية';
        default:
          return grade ?? '';
      }
    }
    return grade ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      child: ClipRRect(
        borderRadius: BorderRadius.all(Radius.circular(10.r)),
        child: Table(
          columnWidths: const {
            0: FlexColumnWidth(3),
            1: FlexColumnWidth(2),
            2: FlexColumnWidth(2),
            3: FlexColumnWidth(1),
          },
          border: TableBorder.all(
            color: TColor.tabColors,
            borderRadius: BorderRadius.circular(15.r),
          ),
          children:
              List.generate(1 + widget.studentInfoModels!.length, (index) {
            if (index == 0) {
              return BuildTableRowWidget(cell: [
                AppStrings.name.tr(),
                AppStrings.busName.tr(),
                AppStrings.grade.tr(),
                AppStrings.show.tr(),
              ], header: true)
                  .build(context);
            } else {
              final newStudentInfoModels = widget.studentInfoModels![index - 1];
              return BuildTableRowWidget(
                cell: [
                  newStudentInfoModels?.name,
                  widget.isFromBus
                      ? widget.busName
                      : newStudentInfoModels?.bus?.name ??
                          AppStrings.notFound.tr(),
                  _translateGrade(newStudentInfoModels?.grade?.name),
                  Icons.more_horiz,
                ],
                color: newStudentInfoModels?.latitude == null
                    ? TColor.darkRed
                    : null,
                onTapDown: (TapDownDetails details) {
                  Helpers.customShowDialog(context,
                      position: details.globalPosition, onTapLocation: () {
                    if (newStudentInfoModels!.latitude != null &&
                        newStudentInfoModels.longitude != null) {
                      Navigator.of(context).pop();
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => StudentAddressScreen(
                            lat: double.parse(newStudentInfoModels.latitude!),
                            long: double.parse(newStudentInfoModels.longitude!),
                          ),
                        ),
                      );
                    } else {
                      Navigator.of(context).pop();
                      Fluttertoast.showToast(
                          msg: "Student location not found.",
                          toastLength: Toast.LENGTH_SHORT,
                          gravity: ToastGravity.BOTTOM,
                          timeInSecForIosWeb: 1,
                          backgroundColor: TColor.redAccent,
                          textColor: TColor.white,
                          fontSize: 16.0);
                    }
                  }, onTapShow: () {
                    Navigator.of(context)
                      ..pop()
                      ..pushNamed(
                        PathRouteName.studentData,
                        arguments: newStudentInfoModels,
                      );
                  }, onTapEdit: () {
                    if (kDebugMode) {
                      print(newStudentInfoModels.toString());
                    }
                    Navigator.of(context)
                      ..pop()
                      ..push(MaterialPageRoute(builder: (ctx) {
                        return AddStudentScreen(
                          isEdit: true,
                          studentModel: newStudentInfoModels,
                          isFromBus: widget.isFromBus,
                          previousBusId: widget.busId,
                        );
                      }));
                  }, onTapDelete: () async {
                    Navigator.of(context).pop();
                    if (widget.isFromBus) {
                      List<String> studentIds = [];
                      studentIds.add(newStudentInfoModels!.id!);
                      bool isSuccess = await BusesRepo().removeStudentFromBus(
                          studentIds: studentIds,
                          busId: newStudentInfoModels.bus_id.toString());
                      if (isSuccess) {
                        context.read<StudentCubit>().getStudentWithBusId(
                            busId: int.parse(
                                newStudentInfoModels.bus_id!.toString()));
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            backgroundColor: TColor.greenSuccess,
                            content: CustomText(
                              text: 'تم حذف الطالب من الباص بنجاح',
                              fontSize: 18,
                              maxLine: 5,
                              color: TColor.white,
                            ),
                          ),
                        );
                      }
                    } else {
                      context
                          .read<DeleteStudentCubit>()
                          .deleteStudent(
                            id: newStudentInfoModels?.id,
                          )
                          .whenComplete(() {
                        context
                            .read<StudentCubit>()
                            .getStudent(page: 1, isFirst: true);
                      });
                    }
                  }, items: [
                    PopupMenuItem(
                      child: Center(
                        child: CustomContainerDialogW(
                          icons: Icons.remove_red_eye,
                          name:
                              '${AppStrings.show.tr()} ${AppStrings.parents.tr()}',
                          onTap: () {
                            //  final parent = ;
                            // ParentCubit.get(context).getParent(isFirst: true,pageNumber: 1);
                            print(
                                '=====================================${newStudentInfoModels?.parent}');
                            // ParentCubit.get(context).getParentData(parentId:newStudentInfoModels?.parent![index].id );/
                            Navigator.of(context)
                              ..pop()
                              ..push(MaterialPageRoute(builder: (ctx) {
                                return ParentScreen(
                                  isFromStudents: true,
                                  dataInfo: newStudentInfoModels?.parent,
                                );
                              }));
                          },
                        ),
                      ),
                    ),
                    PopupMenuItem(
                      child: Center(
                        child: CustomContainerDialogW(
                          icons: CupertinoIcons.doc_on_clipboard,
                          name: AppStrings.SHARE.tr(),
                          onTap: () {
                            final String stringToCopy =
                                '${AppStrings.student.tr()}: ${newStudentInfoModels?.name} \n'
                                '${AppStrings.code.tr()}: ${newStudentInfoModels?.parent_key} \n'
                                '${AppStrings.password.tr()}: ${newStudentInfoModels?.parent_secret}';
                            final newDeepLink =
                                "${ConfigBase.deepLink}add-son?code=${newStudentInfoModels?.parent_key}&password=${newStudentInfoModels?.parent_secret}";
                            Clipboard.setData(
                                ClipboardData(text: stringToCopy));
                            Navigator.of(context).pop();
                            Share.share(newDeepLink);
                          },
                        ),
                      ),
                    ),
                  ]);
                },
              ).build(context);
            }
          }),
        ),
      ),
    );
  }
}
